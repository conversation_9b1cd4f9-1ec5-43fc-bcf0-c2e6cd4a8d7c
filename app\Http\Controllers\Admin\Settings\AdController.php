<?php

namespace App\Http\Controllers\Admin\Settings;

use App\Models\Ad;
use App\Http\Requests\Admin\UpdateAdRequest;
use App\Http\Controllers\Controller;

class AdController extends Controller
{

    public function index()
    {
        return view('admin.settings.ads.index')->with('ads', Ad::all());
    }

    public function edit(Ad $ad)
    {
        return view('admin.settings.ads.edit')->with("ad", $ad);
    }

    public function update(UpdateAdRequest $request, Ad $ad)
    {
        $ad->update([
            'code' => $request['code'],
            'desktop' => $request['desktop'] ?? 0,
            'tablet' => $request['tablet'] ?? 0,
            'phone' => $request['phone'] ?? 0,
            'status' => $request['status'],
        ]);

        showToastr(__('lobage.toastr.update'));
        return redirect(route('admin.settings.ads.index'));
    }

    public function disableAll()
    {
        try {
            $updatedCount = Ad::where('status', 1)->update(['status' => 0]);

            return response()->json([
                'success' => true,
                'message' => __('Successfully disabled :count ads', ['count' => $updatedCount])
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to disable all ads: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to disable ads: :error', ['error' => $e->getMessage()])
            ], 500);
        }
    }

    public function enableAll()
    {
        try {
            $updatedCount = Ad::where('status', 0)->update(['status' => 1]);

            return response()->json([
                'success' => true,
                'message' => __('Successfully enabled :count ads', ['count' => $updatedCount])
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to enable all ads: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to enable ads: :error', ['error' => $e->getMessage()])
            ], 500);
        }
    }
}
