<?php
/**
 * HTML Sitemap Viewer
 * This provides a styled HTML view of the sitemap without relying on XSL
 */

// Get the sitemap XML content
$sitemapUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
              '://' . $_SERVER['HTTP_HOST'] . 
              dirname($_SERVER['REQUEST_URI']) . '/sitemap.xml';

// Fetch sitemap content
$sitemapContent = '';
$urls = [];

try {
    // Try to get sitemap content
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $sitemapUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $sitemapContent = curl_exec($ch);
        curl_close($ch);
    } else {
        $sitemapContent = file_get_contents($sitemapUrl);
    }
    
    // Parse XML
    if (!empty($sitemapContent)) {
        $xml = simplexml_load_string($sitemapContent);
        if ($xml !== false) {
            foreach ($xml->url as $url) {
                $urls[] = [
                    'loc' => (string)$url->loc,
                    'lastmod' => isset($url->lastmod) ? (string)$url->lastmod : '',
                    'changefreq' => isset($url->changefreq) ? (string)$url->changefreq : '',
                    'priority' => isset($url->priority) ? (string)$url->priority : ''
                ];
            }
        }
    }
} catch (Exception $e) {
    error_log('Sitemap viewer error: ' . $e->getMessage());
}

$totalUrls = count($urls);
$highPriorityUrls = count(array_filter($urls, function($url) { return (float)$url['priority'] >= 0.8; }));
$dailyUrls = count(array_filter($urls, function($url) { return $url['changefreq'] === 'daily'; }));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML Sitemap - 1Sec-mail</title>
    <meta name="robots" content="noindex, nofollow">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.2em;
            opacity: 0.95;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .stat {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .stat:hover { transform: translateY(-5px); }
        .stat-number {
            font-size: 2.5em;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }
        .content { padding: 0; overflow-x: auto; }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 18px 15px;
            text-align: left;
            font-weight: 700;
            color: #495057;
            border-bottom: 3px solid #667eea;
            font-size: 0.85em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        td {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }
        tr:hover { background-color: rgba(102, 126, 234, 0.05); }
        tr:nth-child(even) { background-color: rgba(248, 249, 250, 0.5); }
        .url {
            color: #1a73e8;
            text-decoration: none;
            font-weight: 500;
            word-break: break-all;
        }
        .url:hover { color: #0d47a1; text-decoration: underline; }
        .priority {
            font-weight: 700;
            text-align: center;
            padding: 6px 12px;
            border-radius: 20px;
            color: white;
            font-size: 0.85em;
        }
        .priority-high { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); }
        .priority-medium { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); }
        .priority-low { background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); }
        .changefreq, .lastmod {
            text-align: center;
            font-size: 0.9em;
            color: #6c757d;
            font-weight: 500;
        }
        .footer {
            padding: 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #6c757d;
            border-top: 3px solid #667eea;
        }
        .footer a { color: #667eea; text-decoration: none; font-weight: 600; }
        .footer a:hover { text-decoration: underline; }
        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 5px solid #2196f3;
            padding: 25px;
            margin: 30px;
            border-radius: 12px;
        }
        .info-box h3 { margin: 0 0 15px 0; color: #1976d2; font-size: 1.3em; }
        .info-box p { margin: 0; color: #424242; line-height: 1.6; }
        @media (max-width: 768px) {
            body { padding: 10px; }
            .header { padding: 25px 20px; }
            .header h1 { font-size: 2.2em; }
            .stats { grid-template-columns: 1fr; gap: 15px; padding: 20px; }
            th, td { padding: 10px 8px; font-size: 0.85em; }
            .info-box { margin: 20px 10px; padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ XML Sitemap - 1Sec-mail</h1>
            <p>This sitemap contains <?php echo $totalUrls; ?> URLs optimized for search engine crawling and indexing</p>
        </div>
        
        <div class="stats">
            <div class="stat">
                <span class="stat-number"><?php echo $totalUrls; ?></span>
                <span class="stat-label">Total URLs</span>
            </div>
            <div class="stat">
                <span class="stat-number"><?php echo $highPriorityUrls; ?></span>
                <span class="stat-label">High Priority</span>
            </div>
            <div class="stat">
                <span class="stat-number"><?php echo $dailyUrls; ?></span>
                <span class="stat-label">Daily Updates</span>
            </div>
        </div>

        <div class="info-box">
            <h3>📋 About This Sitemap</h3>
            <p>This XML sitemap is designed for search engines like Google, Bing, Yahoo, and other crawlers. It helps them discover and index all the important pages on this website efficiently. The sitemap follows the sitemaps.org protocol for optimal SEO performance.</p>
        </div>

        <div class="content">
            <table>
                <thead>
                    <tr>
                        <th style="width: 45%">🔗 URL Location</th>
                        <th style="width: 15%">⭐ Priority</th>
                        <th style="width: 20%">🔄 Change Frequency</th>
                        <th style="width: 20%">📅 Last Modified</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($urls)): ?>
                        <tr>
                            <td colspan="4" style="text-align: center; padding: 40px; color: #6c757d;">
                                <strong>No URLs found in sitemap</strong><br>
                                <small>The sitemap might be empty or there was an error loading it.</small>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($urls as $url): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo htmlspecialchars($url['loc']); ?>" class="url" target="_blank">
                                        <?php echo htmlspecialchars($url['loc']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php 
                                    $priority = (float)$url['priority'];
                                    $priorityClass = $priority >= 0.8 ? 'priority-high' : ($priority >= 0.5 ? 'priority-medium' : 'priority-low');
                                    ?>
                                    <span class="priority <?php echo $priorityClass; ?>">
                                        <?php echo htmlspecialchars($url['priority']); ?>
                                    </span>
                                </td>
                                <td class="changefreq">
                                    <?php echo htmlspecialchars(ucfirst($url['changefreq'])); ?>
                                </td>
                                <td class="lastmod">
                                    <?php 
                                    if (!empty($url['lastmod'])) {
                                        $date = date('Y-m-d', strtotime($url['lastmod']));
                                        echo htmlspecialchars($date);
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>
                🕒 Generated on <?php echo date('Y-m-d H:i:s T'); ?> | 
                📚 <a href="https://www.sitemaps.org/" target="_blank">Learn about XML Sitemaps</a> | 
                🤖 <a href="https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview" target="_blank">Google SEO Guide</a> |
                📄 <a href="<?php echo htmlspecialchars($sitemapUrl); ?>" target="_blank">View Raw XML</a>
            </p>
        </div>
    </div>
</body>
</html>
