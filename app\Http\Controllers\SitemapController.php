<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class SitemapController extends Controller
{
    public function index()
    {
        try {
            // Check if sitemap is enabled (skip if database not available)
            $sitemapEnabled = true;
            try {
                if (function_exists('getSetting') && $this->isDatabaseAvailable()) {
                    $sitemapEnabled = getSetting('sitemap_enabled', true);
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to check sitemap_enabled setting: ' . $e->getMessage());
            }

            if (!$sitemapEnabled) {
                abort(404);
            }

            // Detect if request is from a search engine bot or human browser
            $isSearchEngineBot = $this->isSearchEngineBot();
            $shouldStyleForHumans = !$isSearchEngineBot && $this->shouldDisplayStyled();

            // Check if database is available
            $databaseAvailable = $this->isDatabaseAvailable();

            // Use different cache keys for styled vs non-styled versions
            $cacheKey = 'sitemap_xml' . ($shouldStyleForHumans ? '_styled' : '_clean');
            $cacheDuration = 60; // Default cache duration

            try {
                if ($databaseAvailable && function_exists('getSetting')) {
                    $cacheDuration = getSetting('sitemap_cache_duration', 60);
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to get sitemap cache duration: ' . $e->getMessage());
            }

            $sitemap = '';

            if ($databaseAvailable) {
                try {
                    // Ensure sitemap settings exist
                    $this->ensureSitemapSettings();

                    $sitemap = Cache::remember($cacheKey, now()->addMinutes($cacheDuration), function () use ($shouldStyleForHumans) {
                        return $this->generateSitemap($shouldStyleForHumans);
                    });
                    Cache::put('sitemap_last_generated', now());
                } catch (\Exception $e) {
                    \Log::error('Failed to generate sitemap from database: ' . $e->getMessage());
                    // Fallback to basic sitemap
                    $sitemap = $this->generateBasicSitemap($shouldStyleForHumans);
                }
            } else {
                // Generate basic sitemap without database
                $sitemap = $this->generateBasicSitemap($shouldStyleForHumans);
            }

            // Ensure we have valid XML content
            if (empty($sitemap) || !str_contains($sitemap, '<urlset')) {
                \Log::warning('Generated sitemap is empty or invalid, using basic sitemap');
                $sitemap = $this->generateBasicSitemap($shouldStyleForHumans);
            }

            // Final validation - ensure XML is well-formed
            if (!$this->isValidXml($sitemap)) {
                \Log::error('Generated sitemap is not valid XML, using minimal fallback');
                $sitemap = $this->generateMinimalSitemap();
            }

            return Response::make($sitemap, 200, [
                'Content-Type' => 'application/xml; charset=UTF-8',
                'Cache-Control' => 'public, max-age=' . ($cacheDuration * 60) . ', must-revalidate',
                'X-Robots-Tag' => 'noindex, follow',
                'ETag' => '"' . md5($sitemap . now()->format('Y-m-d-H')) . '"',
                'Vary' => 'Accept-Encoding',
                'Last-Modified' => now()->format('D, d M Y H:i:s T'),
            ]);

        } catch (\Exception $e) {
            \Log::error('Critical error in sitemap generation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // Return a minimal sitemap as last resort
            $minimalSitemap = $this->generateMinimalSitemap();
            return Response::make($minimalSitemap, 200, [
                'Content-Type' => 'application/xml; charset=UTF-8',
                'Cache-Control' => 'no-cache, must-revalidate',
                'X-Robots-Tag' => 'noindex, follow',
                'Vary' => 'Accept-Encoding',
            ]);
        }
    }

    private function generateSitemap($shouldStyleForHumans = false)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;

        // Add XSL stylesheet for human browsers (not search engine bots)
        if ($shouldStyleForHumans && $this->isXslSupported()) {
            $xml .= '<?xml-stylesheet type="text/xsl" href="' . url('/sitemap.xsl') . '"?>' . PHP_EOL;
        }

        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . PHP_EOL;
        $xml .= '        xmlns:xhtml="http://www.w3.org/1999/xhtml"' . PHP_EOL;
        $xml .= '        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">' . PHP_EOL;
        $xml .= '  <!-- Generated by ' . config('app.name', 'Laravel') . ' on ' . now()->format('Y-m-d H:i:s T') . ' -->' . PHP_EOL;
        $xml .= '  <!-- Optimized for search engine crawling and indexing -->' . PHP_EOL;
        $xml .= '  <!-- Total URLs: ' . $this->getTotalUrlCount() . ' -->' . PHP_EOL;

        // Add homepage with enhanced metadata
        $xml .= $this->addUrl(url('/'), now(), 'daily', '1.0', [], true);

        // Add static pages
        if (!function_exists('getSetting') || getSetting('sitemap_include_pages', true)) {
            $xml .= $this->addPages();
        }

        // Add blog posts and index
        if ((!function_exists('getSetting') || getSetting('sitemap_include_blog', true)) &&
            (!function_exists('getSetting') || getSetting('enable_blog', true))) {
            $xml .= $this->addBlogPosts();
            $xml .= $this->addBlogIndex();
        }

        $xml .= '</urlset>' . PHP_EOL;

        return $xml;
    }

    private function addUrl($url, $lastmod = null, $changefreq = 'weekly', $priority = '0.5', $alternates = [], $isHomepage = false)
    {
        // Ensure URL is properly formatted and canonical
        $canonicalUrl = $this->canonicalizeUrl($url);

        $xml = '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($canonicalUrl) . '</loc>' . PHP_EOL;

        if ($lastmod) {
            $xml .= '    <lastmod>' . $lastmod->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        }

        $xml .= '    <changefreq>' . htmlspecialchars($changefreq) . '</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . number_format((float)$priority, 1) . '</priority>' . PHP_EOL;

        // Add hreflang alternates for multilingual support
        if (!empty($alternates)) {
            foreach ($alternates as $lang => $altUrl) {
                $canonicalAltUrl = $this->canonicalizeUrl($altUrl);
                $xml .= '    <xhtml:link rel="alternate" hreflang="' . htmlspecialchars($lang) . '" href="' . htmlspecialchars($canonicalAltUrl) . '" />' . PHP_EOL;
            }

            // Add x-default for homepage or main language
            if ($isHomepage && !empty($alternates)) {
                $defaultUrl = $this->canonicalizeUrl($url);
                $xml .= '    <xhtml:link rel="alternate" hreflang="x-default" href="' . htmlspecialchars($defaultUrl) . '" />' . PHP_EOL;
            }
        }

        $xml .= '  </url>' . PHP_EOL;

        return $xml;
    }

    private function canonicalizeUrl($url)
    {
        // Ensure URL is properly formatted
        $url = trim($url);

        // Remove trailing slash except for homepage
        if ($url !== url('/') && str_ends_with($url, '/')) {
            $url = rtrim($url, '/');
        }

        // Ensure HTTPS if forced
        if (config('app.force_https', false) || (function_exists('getSetting') && getSetting('https_force', false))) {
            $url = str_replace('http://', 'https://', $url);
        }

        return $url;
    }

    private function addPages()
    {
        $xml = '';
        try {
            $pages = Page::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_pages_priority', '0.8') : '0.8';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_pages_changefreq', 'weekly') : 'weekly';

            foreach ($pages as $page) {
                try {
                    $url = route('page', $page->slug);
                    $alternates = $this->getPageAlternates($page);
                    $xml .= $this->addUrl($url, $page->updated_at, $changefreq, $priority, $alternates);
                } catch (\Exception $e) {
                    // Skip this page if route generation fails
                    \Log::warning('Failed to generate sitemap URL for page: ' . $page->slug, ['error' => $e->getMessage()]);
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to add pages to sitemap: ' . $e->getMessage());
            return '';
        }

        return $xml;
    }

    private function addBlogPosts()
    {
        $xml = '';
        try {
            $posts = BlogPost::where('status', 1)->get();
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';

            foreach ($posts as $post) {
                try {
                    $url = route('posts', $post->slug);
                    $alternates = $this->getBlogPostAlternates($post);
                    $xml .= $this->addUrl($url, $post->updated_at, $changefreq, $priority, $alternates);
                } catch (\Exception $e) {
                    // Skip this post if route generation fails
                    \Log::warning('Failed to generate sitemap URL for blog post: ' . $post->slug, ['error' => $e->getMessage()]);
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to add blog posts to sitemap: ' . $e->getMessage());
            return '';
        }

        return $xml;
    }

    private function addBlogIndex()
    {
        $xml = '';
        try {
            // Check if blog route exists
            if (!\Route::has('blog')) {
                return '';
            }

            $url = route('blog');
            $priority = function_exists('getSetting') ? getSetting('sitemap_blog_priority', '0.7') : '0.7';
            $changefreq = function_exists('getSetting') ? getSetting('sitemap_blog_changefreq', 'weekly') : 'weekly';
            $lastPost = BlogPost::where('status', 1)->latest('updated_at')->first();
            $lastmod = $lastPost ? $lastPost->updated_at : now();

            $alternates = $this->getBlogIndexAlternates();
            $xml .= $this->addUrl($url, $lastmod, $changefreq, $priority, $alternates);
        } catch (\Exception $e) {
            \Log::error('Failed to add blog index to sitemap: ' . $e->getMessage());
            return '';
        }

        return $xml;
    }

    private function getPageAlternates($page)
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $altPage = Page::where('slug', $page->slug)
                        ->where('lang', $language->code)
                        ->where('status', 1)
                        ->first();

                    if ($altPage && \Route::has('page')) {
                        $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                        $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('page', $altPage->slug));
                        $alternates[$language->code] = $altUrl;
                    }
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get page alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function getBlogPostAlternates($post)
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $altPost = BlogPost::where('slug', $post->slug)
                        ->where('lang', $language->code)
                        ->where('status', 1)
                        ->first();

                    if ($altPost && \Route::has('posts')) {
                        $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                        $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('posts', $altPost->slug));
                        $alternates[$language->code] = $altUrl;
                    }
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get blog post alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function getBlogIndexAlternates()
    {
        $alternates = [];
        try {
            if (!class_exists('App\Models\Language') || !\Route::has('blog')) {
                return $alternates;
            }

            $languages = Language::where('status', 1)->get();

            foreach ($languages as $language) {
                try {
                    $currentLang = function_exists('getCurrentLang') ? getCurrentLang() : 'en';
                    $altUrl = str_replace('/' . $currentLang . '/', '/' . $language->code . '/', route('blog'));
                    $alternates[$language->code] = $altUrl;
                } catch (\Exception $e) {
                    // Skip this language if there's an error
                    continue;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get blog index alternates: ' . $e->getMessage());
        }

        return $alternates;
    }

    private function isDatabaseAvailable()
    {
        try {
            // Check if system is installed first
            if (env('SYSTEM_INSTALLED', '0') !== '1') {
                \Log::debug('System not installed, database not available for sitemap');
                return false;
            }

            // Check if database configuration exists
            $dbConfig = config('database.connections.mysql');
            if (empty($dbConfig['database']) || empty($dbConfig['username'])) {
                \Log::debug('Database configuration incomplete for sitemap');
                return false;
            }

            // Test database connection
            \DB::connection()->getPdo();

            // Test if we can actually query the database and check for required tables
            \DB::connection()->select('SELECT 1');

            // Check if settings table exists (required for sitemap settings)
            $tables = \DB::select("SHOW TABLES LIKE 'settings'");
            if (empty($tables)) {
                \Log::debug('Settings table not found, database not ready for sitemap');
                return false;
            }

            return true;
        } catch (\Exception $e) {
            \Log::debug('Database not available for sitemap: ' . $e->getMessage());
            return false;
        }
    }

    private function generateBasicSitemap($shouldStyleForHumans = false)
    {
        try {
            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;

            // Add XSL stylesheet for human browsers (not search engine bots)
            if ($shouldStyleForHumans && $this->isXslSupported()) {
                $xml .= '<?xml-stylesheet type="text/xsl" href="' . url('/sitemap.xsl') . '"?>' . PHP_EOL;
            }

            $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . PHP_EOL;
            $xml .= '        xmlns:xhtml="http://www.w3.org/1999/xhtml">' . PHP_EOL;
            $xml .= '  <!-- Generated by ' . (config('app.name', 'Laravel') ?? 'Website') . ' on ' . now()->format('Y-m-d H:i:s T') . ' -->' . PHP_EOL;

            // Add homepage
            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . htmlspecialchars(url('/')) . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . now()->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
            $xml .= '    <priority>1.0</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;

            // Add common static pages that might exist
            $commonPages = [
                '/about' => ['priority' => '0.8', 'changefreq' => 'monthly'],
                '/contact' => ['priority' => '0.7', 'changefreq' => 'monthly'],
                '/privacy' => ['priority' => '0.6', 'changefreq' => 'yearly'],
                '/terms' => ['priority' => '0.6', 'changefreq' => 'yearly'],
            ];

            foreach ($commonPages as $path => $config) {
                $xml .= '  <url>' . PHP_EOL;
                $xml .= '    <loc>' . htmlspecialchars(url($path)) . '</loc>' . PHP_EOL;
                $xml .= '    <lastmod>' . now()->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
                $xml .= '    <changefreq>' . $config['changefreq'] . '</changefreq>' . PHP_EOL;
                $xml .= '    <priority>' . $config['priority'] . '</priority>' . PHP_EOL;
                $xml .= '  </url>' . PHP_EOL;
            }

            $xml .= '</urlset>' . PHP_EOL;

            return $xml;
        } catch (\Exception $e) {
            \Log::error('Failed to generate basic sitemap: ' . $e->getMessage());

            // Minimal fallback
            $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
            $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
            $xml .= '  <url>' . PHP_EOL;
            $xml .= '    <loc>' . htmlspecialchars(url('/')) . '</loc>' . PHP_EOL;
            $xml .= '    <lastmod>' . now()->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
            $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
            $xml .= '    <priority>1.0</priority>' . PHP_EOL;
            $xml .= '  </url>' . PHP_EOL;
            $xml .= '</urlset>' . PHP_EOL;

            return $xml;
        }
    }

    /**
     * Validate if the XML string is well-formed
     */
    private function isValidXml($xml)
    {
        if (empty($xml)) {
            return false;
        }

        try {
            $dom = new \DOMDocument();
            $dom->loadXML($xml);
            return true;
        } catch (\Exception $e) {
            \Log::error('XML validation failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate a minimal sitemap as absolute fallback
     */
    private function generateMinimalSitemap()
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
        $xml .= '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars(url('/')) . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . now()->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
        $xml .= '    <priority>1.0</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;
        $xml .= '</urlset>' . PHP_EOL;

        return $xml;
    }

    /**
     * Detect if the request is from a search engine bot
     */
    private function isSearchEngineBot()
    {
        $userAgent = request()->header('User-Agent', '');

        // Common search engine bot user agents
        $searchEngineBots = [
            'Googlebot',
            'Bingbot',
            'Slurp',           // Yahoo
            'DuckDuckBot',     // DuckDuckGo
            'Baiduspider',     // Baidu
            'YandexBot',       // Yandex
            'facebookexternalhit', // Facebook
            'Twitterbot',      // Twitter
            'LinkedInBot',     // LinkedIn
            'WhatsApp',        // WhatsApp
            'Applebot',        // Apple
            'ia_archiver',     // Alexa
            'SeznamBot',       // Seznam
            'MJ12bot',         // Majestic
            'AhrefsBot',       // Ahrefs
            'SemrushBot',      // Semrush
            'MauiBot',         // Maui
            'DotBot',          // OpenSiteExplorer
            'BLEXBot',         // BLEXBot
            'UptimeRobot',     // Uptime monitoring
            'StatusCake',      // StatusCake monitoring
            'Pingdom',         // Pingdom monitoring
            'GTmetrix',        // GTmetrix
            'PageSpeed',       // Google PageSpeed
            'Lighthouse',      // Google Lighthouse
            'crawler',         // Generic crawler
            'spider',          // Generic spider
            'bot',             // Generic bot (be careful with this one)
        ];

        foreach ($searchEngineBots as $bot) {
            if (stripos($userAgent, $bot) !== false) {
                \Log::debug("Search engine bot detected: $bot in $userAgent");
                return true;
            }
        }

        // Additional checks for bot-like behavior
        if (empty($userAgent) ||
            stripos($userAgent, 'curl') !== false ||
            stripos($userAgent, 'wget') !== false ||
            stripos($userAgent, 'python') !== false ||
            stripos($userAgent, 'java') !== false ||
            stripos($userAgent, 'php') !== false) {
            \Log::debug("Bot-like user agent detected: $userAgent");
            return true;
        }

        return false;
    }

    /**
     * Determine if we should display styled version
     */
    private function shouldDisplayStyled()
    {
        // Always style if explicitly requested
        if (request()->get('styled') === '1') {
            return true;
        }

        // Don't style if explicitly disabled
        if (request()->get('styled') === '0') {
            return false;
        }

        // Check Accept header for XML preference
        $acceptHeader = request()->header('Accept', '');
        if (stripos($acceptHeader, 'application/xml') !== false &&
            stripos($acceptHeader, 'text/html') === false) {
            return false; // Client specifically wants XML
        }

        // Default to styling for browsers
        return true;
    }

    /**
     * Check if XSL transformation is supported
     */
    private function isXslSupported()
    {
        // Check if XSL file exists
        if (!file_exists(public_path('sitemap.xsl'))) {
            \Log::debug('XSL file not found, skipping stylesheet');
            return false;
        }

        // Check if XSL extension is available (optional, most browsers handle XSL client-side)
        if (!extension_loaded('xsl')) {
            \Log::debug('XSL extension not loaded, but client-side transformation should work');
        }

        return true;
    }

    /**
     * Ensure sitemap settings exist in database
     */
    private function ensureSitemapSettings()
    {
        try {
            if (!$this->isDatabaseAvailable()) {
                return;
            }

            $defaultSettings = [
                'sitemap_enabled' => '1',
                'sitemap_include_pages' => '1',
                'sitemap_include_blog' => '1',
                'sitemap_pages_priority' => '0.8',
                'sitemap_blog_priority' => '0.7',
                'sitemap_pages_changefreq' => 'weekly',
                'sitemap_blog_changefreq' => 'weekly',
                'sitemap_cache_duration' => '60',
            ];

            foreach ($defaultSettings as $key => $value) {
                \DB::table('settings')->updateOrInsert(
                    ['key' => $key],
                    [
                        'key' => $key,
                        'value' => $value,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );
            }
        } catch (\Exception $e) {
            \Log::debug('Error ensuring sitemap settings: ' . $e->getMessage());
        }
    }

    /**
     * Get total URL count for metadata
     */
    private function getTotalUrlCount()
    {
        try {
            if (!$this->isDatabaseAvailable()) {
                return 6; // Basic static pages count
            }

            $this->ensureSitemapSettings();

            $count = 1; // Homepage

            // Add pages count
            if (!function_exists('getSetting') || getSetting('sitemap_include_pages', true)) {
                $count += \DB::table('pages')->where('status', 1)->count();
            }

            // Add blog posts count
            if ((!function_exists('getSetting') || getSetting('sitemap_include_blog', true)) &&
                (!function_exists('getSetting') || getSetting('enable_blog', true))) {
                $count += \DB::table('blog_posts')->where('status', 1)->count();
                $count += 1; // Blog index
            }

            return $count;
        } catch (\Exception $e) {
            \Log::debug('Error counting URLs: ' . $e->getMessage());
            return 6; // Fallback count
        }
    }
}
