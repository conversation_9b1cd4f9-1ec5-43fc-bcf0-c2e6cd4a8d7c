<?php

namespace App\Services;

use App\Models\Page;
use App\Models\BlogPost;
use App\Models\BlogCategory;

class StructuredDataService
{
    private function safeSetting($key, $default = null)
    {
        try {
            return function_exists('getSetting') ? getSetting($key, $default) : $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
    public function generateStructuredData($type = 'general', $data = null)
    {
        try {
            if (!$this->safeSetting('structured_data_enabled', true)) {
                return '';
            }

            $schemas = [];

            switch ($type) {
                case 'general':
                    $schemas = $this->getGeneralSchemas();
                    break;
                case 'article':
                    $schemas = $this->getArticleSchema($data);
                    break;
                case 'breadcrumbs':
                    $schemas = $this->getBreadcrumbSchema($data);
                    break;
                case 'page':
                    $schemas = $this->getPageSchema($data);
                    break;
                case 'service':
                    $schemas = $this->getServiceSchema();
                    break;
                case 'software':
                    $schemas = $this->getSoftwareApplicationSchema();
                    break;
                case 'faq':
                    $schemas = $this->getFAQSchema($data);
                    break;
            }

            if (empty($schemas)) {
                return '';
            }

            return $this->renderJsonLd($schemas);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('StructuredDataService Error: ' . $e->getMessage());
            return '';
        }
    }

    private function getGeneralSchemas()
    {
        $schemas = [];

        try {
            if ($this->safeSetting('structured_data_organization', true)) {
                $schemas[] = $this->getOrganizationSchema();
            }

            if ($this->safeSetting('structured_data_website', true)) {
                $schemas[] = $this->getWebsiteSchema();
            }

            if ($this->safeSetting('structured_data_service', true)) {
                $schemas[] = $this->getServiceSchema();
            }

            if ($this->safeSetting('structured_data_software', true)) {
                $schemas[] = $this->getSoftwareApplicationSchema();
            }

            if ($this->safeSetting('structured_data_faq', true)) {
                $faqSchemas = $this->getFAQSchema();
                if (!empty($faqSchemas)) {
                    $schemas = array_merge($schemas, $faqSchemas);
                }
            }

            // Add WebPage schema for better page understanding
            $schemas[] = $this->getWebPageSchema();
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('General Schemas Error: ' . $e->getMessage());
        }

        return $schemas;
    }

    private function getOrganizationSchema()
    {
        $siteName = $this->safeSetting('site_name', 'Temporary Email Service');
        $orgName = $this->safeSetting('organization_name', $siteName);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $orgName,
            'url' => $this->safeSetting('organization_url', url('/')),
            'identifier' => url('/'), // Unique identifier
            'foundingDate' => $this->safeSetting('organization_founding_date', date('Y')),
            'description' => $this->safeSetting('organization_description', 'Provider of secure temporary email services for privacy protection'),
            'knowsAbout' => [
                'Email Privacy',
                'Temporary Email',
                'Spam Protection',
                'Online Privacy',
                'Digital Security'
            ],
            'hasOfferCatalog' => [
                '@type' => 'OfferCatalog',
                'name' => 'Email Services',
                'itemListElement' => [
                    [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => 'Temporary Email Service'
                        ]
                    ]
                ]
            ]
        ];

        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['logo'] = [
                '@type' => 'ImageObject',
                'url' => asset($logo),
                'width' => '600',
                'height' => '60'
            ];
        }

        if ($description = $this->safeSetting('organization_description')) {
            $schema['description'] = $description;
        }

        // Add slogan/tagline for better branding
        if ($slogan = $this->safeSetting('organization_slogan')) {
            $schema['slogan'] = $slogan;
        }

        // Contact information
        $contactPoint = [];
        if ($telephone = $this->safeSetting('organization_telephone')) {
            $contactPoint['telephone'] = $telephone;
        }
        if ($email = $this->safeSetting('organization_email')) {
            $contactPoint['email'] = $email;
        }
        if ($contactType = $this->safeSetting('organization_contact_type')) {
            $contactPoint['contactType'] = $contactType;
        }

        if (!empty($contactPoint)) {
            $contactPoint['@type'] = 'ContactPoint';
            $schema['contactPoint'] = $contactPoint;
        }

        // Address
        $address = [];
        if ($street = $this->safeSetting('organization_address_street')) {
            $address['streetAddress'] = $street;
        }
        if ($city = $this->safeSetting('organization_address_city')) {
            $address['addressLocality'] = $city;
        }
        if ($region = $this->safeSetting('organization_address_region')) {
            $address['addressRegion'] = $region;
        }
        if ($postal = $this->safeSetting('organization_address_postal')) {
            $address['postalCode'] = $postal;
        }
        if ($country = $this->safeSetting('organization_address_country')) {
            $address['addressCountry'] = $country;
        }

        if (!empty($address)) {
            $address['@type'] = 'PostalAddress';
            $schema['address'] = $address;
        }

        // Social media
        $socialMedia = [];
        if ($facebook = $this->safeSetting('social_media_facebook')) {
            $socialMedia[] = $facebook;
        }
        if ($twitter = $this->safeSetting('social_media_twitter')) {
            $socialMedia[] = $twitter;
        }
        if ($instagram = $this->safeSetting('social_media_instagram')) {
            $socialMedia[] = $instagram;
        }
        if ($linkedin = $this->safeSetting('social_media_linkedin')) {
            $socialMedia[] = $linkedin;
        }
        if ($youtube = $this->safeSetting('social_media_youtube')) {
            $socialMedia[] = $youtube;
        }

        if (!empty($socialMedia)) {
            $schema['sameAs'] = $socialMedia;
        }

        return $schema;
    }

    private function getWebPageSchema()
    {
        $siteName = $this->safeSetting('site_name', 'Temporary Email Service');

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $siteName,
            'description' => $this->safeSetting('site_description', 'Secure temporary email service for privacy protection and spam prevention'),
            'url' => url('/'),
            'inLanguage' => function_exists('getCurrentLang') ? getCurrentLang() : 'en',
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => $siteName,
                'url' => url('/')
            ],
            'about' => [
                '@type' => 'Thing',
                'name' => 'Temporary Email Services'
            ],
            'mainEntity' => [
                '@type' => 'Service',
                'name' => 'Temporary Email Service',
                'description' => 'Secure temporary email service for privacy protection'
            ],
            'breadcrumb' => [
                '@type' => 'BreadcrumbList',
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => 1,
                        'name' => 'Home',
                        'item' => url('/')
                    ]
                ]
            ]
        ];

        return $schema;
    }

    private function getWebsiteSchema()
    {
        $siteName = $this->safeSetting('site_name', 'Temporary Email Service');
        $orgName = $this->safeSetting('organization_name', $siteName);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $siteName,
            'alternateName' => $this->safeSetting('site_alternate_name', $siteName),
            'url' => url('/'),
            'description' => $this->safeSetting('site_description', 'Secure temporary email service for privacy protection and spam prevention'),
            'inLanguage' => function_exists('getCurrentLang') ? getCurrentLang() : 'en',
            'isAccessibleForFree' => true,
            'publisher' => [
                '@type' => 'Organization',
                'name' => $orgName,
                'url' => url('/')
            ],
            'keywords' => 'temporary email, disposable email, privacy, spam protection, anonymous email',
            'genre' => 'Email Service',
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Internet Users'
            ]
        ];

        // Enhanced search action with multiple search types
        $searchActions = [];

        // Main site search
        $searchActions[] = [
            '@type' => 'SearchAction',
            'target' => [
                '@type' => 'EntryPoint',
                'urlTemplate' => url('/') . '?search={search_term_string}',
                'actionPlatform' => [
                    'http://schema.org/DesktopWebPlatform',
                    'http://schema.org/MobileWebPlatform'
                ]
            ],
            'query-input' => 'required name=search_term_string'
        ];

        // Email domain search (if applicable)
        if ($this->safeSetting('enable_domain_search', true)) {
            $searchActions[] = [
                '@type' => 'SearchAction',
                'target' => [
                    '@type' => 'EntryPoint',
                    'urlTemplate' => url('/') . '?domain={domain_name}',
                    'actionPlatform' => [
                        'http://schema.org/DesktopWebPlatform',
                        'http://schema.org/MobileWebPlatform'
                    ]
                ],
                'query-input' => 'required name=domain_name'
            ];
        }

        $schema['potentialAction'] = $searchActions;

        // Add website categories/topics
        $schema['about'] = [
            [
                '@type' => 'Thing',
                'name' => 'Email Privacy'
            ],
            [
                '@type' => 'Thing',
                'name' => 'Temporary Email Services'
            ],
            [
                '@type' => 'Thing',
                'name' => 'Spam Protection'
            ]
        ];

        return $schema;
    }

    private function getArticleSchema($post)
    {
        if (!$this->safeSetting('structured_data_articles', true) || !$post) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $post->title,
            'description' => $post->meta_description ?: $post->description,
            'url' => route('posts', $post->slug),
            'datePublished' => $post->created_at->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'inLanguage' => $post->lang ?? (function_exists('getCurrentLang') ? getCurrentLang() : 'en'),
            'wordCount' => str_word_count(strip_tags($post->content ?? '')),
            'author' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
                'url' => url('/')
            ],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => route('posts', $post->slug)
            ]
        ];

        // Enhanced image handling for rich snippets
        if ($post->image) {
            $schema['image'] = [
                '@type' => 'ImageObject',
                'url' => asset($post->image),
                'width' => '1200',
                'height' => '630'
            ];
        }

        // Publisher logo for rich snippets
        if ($logo = $this->safeSetting('organization_logo')) {
            $schema['publisher']['logo'] = [
                '@type' => 'ImageObject',
                'url' => asset($logo),
                'width' => '600',
                'height' => '60'
            ];
        }

        // Article section and category
        if ($post->category) {
            $schema['articleSection'] = $post->category->name;
            $schema['about'] = [
                '@type' => 'Thing',
                'name' => $post->category->name
            ];
        }

        // Enhanced keywords handling
        if ($post->tags) {
            $tags = explode(',', $post->tags);
            $schema['keywords'] = array_map('trim', $tags);
        }

        // Add reading time estimate
        $wordCount = str_word_count(strip_tags($post->content ?? ''));
        $readingTime = max(1, round($wordCount / 200)); // Average reading speed
        $schema['timeRequired'] = 'PT' . $readingTime . 'M';

        // Add article type based on content
        if (stripos($post->title, 'how to') !== false || stripos($post->content, 'step') !== false) {
            $schema['@type'] = 'HowTo';
        } elseif (stripos($post->title, 'review') !== false) {
            $schema['@type'] = 'Review';
        }

        return [$schema];
    }

    private function getPageSchema($page)
    {
        if (!$page) {
            return [];
        }

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => $page->title,
            'description' => $page->meta_description,
            'url' => route('page', $page->slug),
            'datePublished' => $page->created_at->toISOString(),
            'dateModified' => $page->updated_at->toISOString(),
            'isPartOf' => [
                '@type' => 'WebSite',
                'name' => $this->safeSetting('site_name', 'Website'),
                'url' => url('/')
            ]
        ];

        return [$schema];
    }

    private function getBreadcrumbSchema($breadcrumbs)
    {
        if (!$this->safeSetting('structured_data_breadcrumbs', true) || empty($breadcrumbs)) {
            return [];
        }

        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [[
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $listItems
        ]];
    }

    private function renderJsonLd($schemas)
    {
        if (empty($schemas)) {
            return '';
        }

        $output = '';
        foreach ($schemas as $schema) {
            $output .= '<script type="application/ld+json">' . PHP_EOL;
            $output .= json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $output .= '</script>' . PHP_EOL;
        }

        return $output;
    }

    public function getBreadcrumbs($currentPage = null, $category = null)
    {
        $breadcrumbs = [
            [
                'name' => __('Home'),
                'url' => url('/')
            ]
        ];

        if ($category) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
            $breadcrumbs[] = [
                'name' => $category->name,
                'url' => route('category', $category->slug)
            ];
        } elseif (request()->is('*/blog*')) {
            $breadcrumbs[] = [
                'name' => __('Blog'),
                'url' => route('blog')
            ];
        }

        if ($currentPage) {
            $breadcrumbs[] = [
                'name' => $currentPage,
                'url' => url()->current()
            ];
        }

        return $breadcrumbs;
    }

    /**
     * Generate Service schema for temporary email service
     */
    private function getServiceSchema()
    {
        $siteName = $this->safeSetting('site_name', 'Temporary Email Service');
        $serviceName = $this->safeSetting('service_name', $siteName);
        $orgName = $this->safeSetting('organization_name', $siteName);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $serviceName,
            'description' => $this->safeSetting('service_description', 'Secure temporary email service for privacy protection and spam prevention'),
            'url' => url('/'),
            'provider' => [
                '@type' => 'Organization',
                'name' => $orgName,
                'url' => url('/')
            ],
            'serviceType' => 'Email Service',
            'category' => 'Internet Service',
            'audience' => [
                '@type' => 'Audience',
                'audienceType' => 'Internet Users'
            ],
            'availableChannel' => [
                '@type' => 'ServiceChannel',
                'serviceUrl' => url('/'),
                'serviceName' => $serviceName,
                'availableLanguage' => function_exists('getCurrentLang') ? getCurrentLang() : 'en'
            ],
            'termsOfService' => url('/terms'),
            'privacyPolicy' => url('/privacy')
        ];

        // Add service features
        $features = [];
        if ($this->safeSetting('service_feature_privacy', true)) {
            $features[] = 'Privacy Protection';
        }
        if ($this->safeSetting('service_feature_temporary', true)) {
            $features[] = 'Temporary Email Addresses';
        }
        if ($this->safeSetting('service_feature_anonymous', true)) {
            $features[] = 'Anonymous Email';
        }
        if ($this->safeSetting('service_feature_spam_protection', true)) {
            $features[] = 'Spam Protection';
        }

        if (!empty($features)) {
            $schema['hasOfferCatalog'] = [
                '@type' => 'OfferCatalog',
                'name' => 'Service Features',
                'itemListElement' => array_map(function($feature) {
                    return [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => $feature
                        ]
                    ];
                }, $features)
            ];
        }

        // Add area served if specified
        if ($areaServed = $this->safeSetting('service_area_served')) {
            $schema['areaServed'] = [
                '@type' => 'Place',
                'name' => $areaServed
            ];
        }

        return $schema;
    }

    /**
     * Generate SoftwareApplication schema for the email service
     */
    private function getSoftwareApplicationSchema()
    {
        $siteName = $this->safeSetting('site_name', 'Temporary Email Service');
        $appName = $this->safeSetting('app_name', $siteName . ' App');
        $orgName = $this->safeSetting('organization_name', $siteName);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'SoftwareApplication',
            'name' => $appName,
            'description' => $this->safeSetting('app_description', 'Web-based temporary email application for secure and private email communication'),
            'url' => url('/'),
            'applicationCategory' => 'WebApplication',
            'applicationSubCategory' => 'Email Application',
            'operatingSystem' => 'Web Browser',
            'browserRequirements' => 'Requires JavaScript. Requires HTML5.',
            'permissions' => 'No special permissions required',
            'isAccessibleForFree' => true,
            'offers' => [
                '@type' => 'Offer',
                'price' => '0',
                'priceCurrency' => 'USD',
                'availability' => 'https://schema.org/InStock'
            ],
            'aggregateRating' => [
                '@type' => 'AggregateRating',
                'ratingValue' => '4.5',
                'ratingCount' => '100',
                'bestRating' => '5',
                'worstRating' => '1'
            ]
        ];

        // Add software version if available
        if ($version = $this->safeSetting('app_version', '1.0')) {
            $schema['softwareVersion'] = $version;
        }

        // Add author/developer
        $schema['author'] = [
            '@type' => 'Organization',
            'name' => $this->safeSetting('organization_name', $this->safeSetting('site_name', 'Website')),
            'url' => url('/')
        ];

        // Add features
        $features = [];
        if ($this->safeSetting('app_feature_realtime', true)) {
            $features[] = 'Real-time email reception';
        }
        if ($this->safeSetting('app_feature_multiple_domains', true)) {
            $features[] = 'Multiple domain support';
        }
        if ($this->safeSetting('app_feature_auto_delete', true)) {
            $features[] = 'Automatic email deletion';
        }
        if ($this->safeSetting('app_feature_mobile_friendly', true)) {
            $features[] = 'Mobile-friendly interface';
        }

        if (!empty($features)) {
            $schema['featureList'] = $features;
        }

        return $schema;
    }

    /**
     * Generate FAQ schema from FAQ model data
     */
    private function getFAQSchema($faqs = null)
    {
        try {
            // If no FAQs provided, try to get from database
            if (!$faqs && class_exists('App\Models\Faq')) {
                $faqs = \App\Models\Faq::where('status', 1)
                    ->where('lang', function_exists('getCurrentLang') ? getCurrentLang() : 'en')
                    ->limit(10) // Limit to prevent too large schema
                    ->get();
            }

            if (empty($faqs) || !is_iterable($faqs)) {
                // Provide default FAQs when database is not available
                $faqs = [
                    (object)[
                        'title' => 'What is a temporary email?',
                        'content' => 'A temporary email is a disposable email address that can be used to receive emails without revealing your real email address. It helps protect your privacy and prevents spam.'
                    ],
                    (object)[
                        'title' => 'How long do temporary emails last?',
                        'content' => 'Temporary emails typically last for a limited time, usually from a few minutes to several hours, depending on the service configuration.'
                    ],
                    (object)[
                        'title' => 'Is it safe to use temporary emails?',
                        'content' => 'Yes, temporary emails are safe to use for non-sensitive registrations and verifications. However, avoid using them for important accounts or sensitive information.'
                    ],
                    (object)[
                        'title' => 'Can I send emails from a temporary address?',
                        'content' => 'Most temporary email services are designed for receiving emails only. They typically do not support sending emails from the temporary address.'
                    ]
                ];
            }

            $questions = [];
            foreach ($faqs as $faq) {
                $questions[] = [
                    '@type' => 'Question',
                    'name' => $faq->title ?? $faq->question ?? '',
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => strip_tags($faq->content ?? $faq->answer ?? '')
                    ]
                ];
            }

            if (empty($questions)) {
                return [];
            }

            return [[
                '@context' => 'https://schema.org',
                '@type' => 'FAQPage',
                'mainEntity' => $questions
            ]];

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('FAQ Schema Error: ' . $e->getMessage());
            return [];
        }
    }
}
