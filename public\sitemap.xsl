<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0"
                xmlns:html="http://www.w3.org/TR/REC-html40"
                xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9"
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
    <xsl:output method="html" version="1.0" encoding="UTF-8" indent="yes"
                doctype-public="-//W3C//DTD XHTML 1.0 Transitional//EN"
                doctype-system="http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"/>
    <xsl:template match="/">
        <html xmlns="http://www.w3.org/1999/xhtml" lang="en">
            <head>
                <title>XML Sitemap - 1Sec-mail</title>
                <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta name="robots" content="noindex, nofollow" />
                <meta name="description" content="XML Sitemap for search engines containing all important pages and their metadata." />
                <!-- VPS/DirectAdmin Compatibility Meta Tags -->
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="format-detection" content="telephone=no" />
                <style type="text/css">
                    /* VPS/DirectAdmin Optimized Styles */
                    /* Reset and base styles */
                    * {
                        box-sizing: border-box;
                        margin: 0;
                        padding: 0;
                    }

                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                        font-size: 14px;
                        line-height: 1.6;
                        color: #2c3e50;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        margin: 0;
                        padding: 20px;
                        min-height: 100vh;
                        /* Performance optimizations for VPS */
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                        text-rendering: optimizeLegibility;
                    }
                    .container {
                        max-width: 1400px;
                        margin: 0 auto;
                        background: white;
                        border-radius: 16px;
                        box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(0,0,0,0.1);
                        overflow: hidden;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255,255,255,0.2);
                    }
                    .header {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        padding: 40px 30px;
                        text-align: center;
                        position: relative;
                        overflow: hidden;
                    }
                    .header::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
                        opacity: 0.3;
                    }
                    .header h1 {
                        margin: 0;
                        font-size: 3em;
                        font-weight: 700;
                        position: relative;
                        z-index: 1;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    }
                    .header p {
                        margin: 15px 0 0 0;
                        opacity: 0.95;
                        font-size: 1.2em;
                        position: relative;
                        z-index: 1;
                        font-weight: 300;
                    }
                    .stats {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        padding: 30px;
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-bottom: 1px solid #dee2e6;
                    }
                    .stat {
                        text-align: center;
                        padding: 20px;
                        background: white;
                        border-radius: 12px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
                        transition: transform 0.3s ease, box-shadow 0.3s ease;
                        border: 1px solid rgba(102, 126, 234, 0.1);
                    }
                    .stat:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                    }
                    .stat-number {
                        font-size: 2.5em;
                        font-weight: 800;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                        display: block;
                        margin-bottom: 8px;
                    }
                    .stat-label {
                        color: #6c757d;
                        font-size: 0.85em;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        font-weight: 600;
                    }
                    .content {
                        padding: 0;
                        overflow-x: auto;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 0;
                        font-size: 0.95em;
                        background: white;
                    }
                    th {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        padding: 18px 15px;
                        text-align: left;
                        font-weight: 700;
                        color: #495057;
                        border-bottom: 3px solid #667eea;
                        font-size: 0.85em;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        position: sticky;
                        top: 0;
                        z-index: 10;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }
                    td {
                        padding: 15px;
                        border-bottom: 1px solid #f1f3f4;
                        vertical-align: middle;
                        transition: background-color 0.2s ease;
                    }
                    tr:hover {
                        background-color: rgba(102, 126, 234, 0.05);
                        transform: scale(1.001);
                    }
                    tr:nth-child(even) {
                        background-color: rgba(248, 249, 250, 0.5);
                    }
                    .url {
                        color: #1a73e8;
                        text-decoration: none;
                        word-break: break-all;
                        font-weight: 500;
                        transition: color 0.2s ease;
                        position: relative;
                    }
                    .url:hover {
                        color: #0d47a1;
                        text-decoration: none;
                    }
                    .url:hover::after {
                        content: '🔗';
                        margin-left: 8px;
                        opacity: 0.7;
                    }
                    .priority {
                        font-weight: 700;
                        text-align: center;
                        padding: 6px 12px;
                        border-radius: 20px;
                        color: white;
                        font-size: 0.85em;
                        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
                    }
                    .priority-high {
                        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
                    }
                    .priority-medium {
                        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                        box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
                    }
                    .priority-low {
                        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
                    }
                    .changefreq {
                        text-align: center;
                        font-size: 0.9em;
                        color: #6c757d;
                        font-weight: 600;
                        text-transform: capitalize;
                    }
                    .lastmod {
                        text-align: center;
                        font-size: 0.9em;
                        color: #6c757d;
                        font-weight: 500;
                        font-family: 'Courier New', monospace;
                    }
                    .footer {
                        padding: 30px;
                        text-align: center;
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        color: #6c757d;
                        font-size: 0.95em;
                        border-top: 3px solid #667eea;
                        font-weight: 500;
                    }
                    .footer a {
                        transition: color 0.2s ease;
                    }
                    .footer a:hover {
                        color: #495057 !important;
                        text-decoration: underline !important;
                    }
                    .info-box {
                        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
                        border-left: 5px solid #2196f3;
                        padding: 25px;
                        margin: 30px;
                        border-radius: 12px;
                        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.1);
                        position: relative;
                        overflow: hidden;
                    }
                    .info-box::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 100px;
                        height: 100px;
                        background: rgba(33, 150, 243, 0.1);
                        border-radius: 50%;
                        transform: translate(30px, -30px);
                    }
                    .info-box h3 {
                        margin: 0 0 15px 0;
                        color: #1976d2;
                        font-size: 1.3em;
                        font-weight: 700;
                        position: relative;
                        z-index: 1;
                    }
                    .info-box p {
                        margin: 0;
                        color: #424242;
                        line-height: 1.6;
                        position: relative;
                        z-index: 1;
                        font-size: 1.05em;
                    }

                    /* SEO Enhancement Box */
                    .seo-tips {
                        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
                        border-left: 5px solid #4caf50;
                        padding: 25px;
                        margin: 30px;
                        border-radius: 12px;
                        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.1);
                    }
                    .seo-tips h3 {
                        margin: 0 0 15px 0;
                        color: #2e7d32;
                        font-size: 1.3em;
                        font-weight: 700;
                    }
                    .seo-tips ul {
                        margin: 0;
                        padding-left: 20px;
                        color: #424242;
                        line-height: 1.6;
                    }
                    .seo-tips li {
                        margin-bottom: 8px;
                        font-size: 1.05em;
                    }
                    /* Performance optimizations */
                    .container {
                        will-change: transform;
                    }
                    table {
                        contain: layout style paint;
                    }

                    /* Loading animation */
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(20px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                    .container {
                        animation: fadeIn 0.6s ease-out;
                    }

                    /* Responsive design */
                    @media (max-width: 1200px) {
                        .container { margin: 10px; }
                        .stats { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); }
                    }
                    @media (max-width: 768px) {
                        body { padding: 10px; }
                        .header { padding: 25px 20px; }
                        .header h1 { font-size: 2.2em; }
                        .header p { font-size: 1.1em; }
                        .stats {
                            grid-template-columns: 1fr;
                            gap: 15px;
                            padding: 20px;
                        }
                        .stat { padding: 15px; }
                        .stat-number { font-size: 2em; }
                        table { font-size: 0.85em; }
                        th, td { padding: 10px 8px; }
                        .info-box, .seo-tips {
                            margin: 20px 10px;
                            padding: 20px;
                        }
                        .url { word-break: break-word; }
                    }
                    @media (max-width: 480px) {
                        .header h1 { font-size: 1.8em; }
                        .header p { font-size: 1em; }
                        th, td { padding: 8px 6px; font-size: 0.8em; }
                        .priority { padding: 4px 8px; font-size: 0.75em; }
                    }
                </style>
                <!-- Fallback for browsers that don't support XSL -->
                <noscript>
                    <style>
                        .xsl-fallback { display: block !important; }
                        .xsl-content { display: none !important; }
                    </style>
                </noscript>
            </head>
            <body>
                <!-- XSL Transformation Content -->
                <div class="xsl-content">
                    <div class="container">
                        <div class="header">
                            <h1>🗺️ XML Sitemap - 1Sec-mail</h1>
                            <p>This sitemap contains <xsl:value-of select="count(sitemap:urlset/sitemap:url)"/> URLs optimized for search engine crawling and indexing</p>
                        </div>
                    
                    <div class="stats">
                        <div class="stat">
                            <span class="stat-number"><xsl:value-of select="count(sitemap:urlset/sitemap:url)"/></span>
                            <span class="stat-label">Total URLs</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number"><xsl:value-of select="count(sitemap:urlset/sitemap:url[sitemap:priority='1.0'])"/></span>
                            <span class="stat-label">High Priority</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number"><xsl:value-of select="count(sitemap:urlset/sitemap:url[sitemap:changefreq='daily'])"/></span>
                            <span class="stat-label">Daily Updates</span>
                        </div>
                    </div>

                    <div class="info-box">
                        <h3>📋 About This Sitemap</h3>
                        <p>This XML sitemap is designed for search engines like Google, Bing, Yahoo, and other crawlers. It helps them discover and index all the important pages on this website efficiently. The sitemap is automatically updated when new content is published and follows the sitemaps.org protocol for optimal SEO performance.</p>
                    </div>

                    <div class="seo-tips">
                        <h3>🚀 SEO Optimization Features</h3>
                        <ul>
                            <li><strong>Priority Levels:</strong> Pages are ranked by importance (1.0 = highest priority)</li>
                            <li><strong>Change Frequency:</strong> Indicates how often content is updated</li>
                            <li><strong>Last Modified:</strong> Shows when each page was last updated</li>
                            <li><strong>Automatic Updates:</strong> Sitemap refreshes when content changes</li>
                            <li><strong>Search Engine Friendly:</strong> Follows XML sitemap protocol standards</li>
                            <li><strong>Fast Crawling:</strong> Optimized structure for quick bot processing</li>
                        </ul>
                    </div>

                    <div class="content">
                        <table>
                            <thead>
                                <tr>
                                    <th style="width: 45%">🔗 URL Location</th>
                                    <th style="width: 15%">⭐ Priority</th>
                                    <th style="width: 20%">🔄 Change Frequency</th>
                                    <th style="width: 20%">📅 Last Modified</th>
                                </tr>
                            </thead>
                            <tbody>
                                <xsl:for-each select="sitemap:urlset/sitemap:url">
                                    <tr>
                                        <td>
                                            <a href="{sitemap:loc}" class="url" target="_blank">
                                                <xsl:value-of select="sitemap:loc"/>
                                            </a>
                                        </td>
                                        <td class="priority">
                                            <xsl:attribute name="class">
                                                priority
                                                <xsl:choose>
                                                    <xsl:when test="sitemap:priority &gt;= 0.8"> priority-high</xsl:when>
                                                    <xsl:when test="sitemap:priority &gt;= 0.5"> priority-medium</xsl:when>
                                                    <xsl:otherwise> priority-low</xsl:otherwise>
                                                </xsl:choose>
                                            </xsl:attribute>
                                            <xsl:value-of select="sitemap:priority"/>
                                        </td>
                                        <td class="changefreq">
                                            <xsl:value-of select="sitemap:changefreq"/>
                                        </td>
                                        <td class="lastmod">
                                            <xsl:value-of select="substring(sitemap:lastmod, 1, 10)"/>
                                        </td>
                                    </tr>
                                </xsl:for-each>
                            </tbody>
                        </table>
                    </div>

                    <div class="footer">
                        <p>
                            🕒 Generated on <xsl:value-of select="format-dateTime(current-dateTime(), '[Y0001]-[M01]-[D01] [H01]:[m01]:[s01]')"/> |
                            📚 Learn more about <a href="https://www.sitemaps.org/" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600;">XML Sitemaps</a> |
                            🤖 <a href="https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview" target="_blank" style="color: #667eea; text-decoration: none; font-weight: 600;">Google SEO Guide</a>
                        </p>
                    </div>
                </div>
                </div>

                <!-- Fallback content for browsers that don't support XSL -->
                <div class="xsl-fallback" style="display: none;">
                    <div style="max-width: 800px; margin: 50px auto; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); font-family: Arial, sans-serif;">
                        <h1 style="color: #333; margin-bottom: 20px;">XML Sitemap</h1>
                        <p style="color: #666; margin-bottom: 20px;">This is an XML sitemap for search engines. Your browser doesn't support XSL transformation.</p>
                        <p style="color: #666;">
                            <a href="sitemap-viewer.php" style="color: #667eea; text-decoration: none; font-weight: bold;">View Styled Sitemap →</a>
                        </p>
                    </div>
                </div>

                <!-- JavaScript fallback detection -->
                <script type="text/javascript">
                    // Check if XSL transformation worked
                    if (document.querySelector('.xsl-content .container') === null) {
                        // XSL failed, show fallback
                        var fallback = document.querySelector('.xsl-fallback');
                        if (fallback) {
                            fallback.style.display = 'block';
                        }
                    }
                </script>
            </body>
        </html>
    </xsl:template>
</xsl:stylesheet>
