<?php
/**
 * Debug script for sitemap issues
 * Access this via: https://yourdomain.com/debug-sitemap.php
 */

// Enable error display for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Sitemap Debug Information</h1>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;}</style>";

// Test 1: Check PHP version and extensions
echo "<h2>1. PHP Environment</h2>";
echo "<div class='info'>PHP Version: " . PHP_VERSION . "</div>";

$requiredExtensions = ['xml', 'dom', 'simplexml'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✓ Extension '$ext' is loaded</div>";
    } else {
        echo "<div class='error'>✗ Extension '$ext' is NOT loaded</div>";
    }
}

// Test 2: Check file permissions
echo "<h2>2. File Permissions</h2>";
$files = [
    'sitemap.xml.php',
    'sitemap-viewer.php',
    '.htaccess'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo "<div class='success'>✓ $file exists (permissions: $perms)</div>";
    } else {
        echo "<div class='error'>✗ $file does not exist</div>";
    }
}

// Test 3: Check Laravel files
echo "<h2>3. Laravel Installation</h2>";
$laravelFiles = [
    '../vendor/autoload.php',
    '../bootstrap/app.php',
    '../.env'
];

foreach ($laravelFiles as $file) {
    if (file_exists($file)) {
        echo "<div class='success'>✓ $file exists</div>";
    } else {
        echo "<div class='error'>✗ $file does not exist</div>";
    }
}

// Test 4: Check .env configuration
echo "<h2>4. Environment Configuration</h2>";
if (file_exists('../.env')) {
    $envContent = file_get_contents('../.env');
    
    // Check system installation
    if (preg_match('/SYSTEM_INSTALLED\s*=\s*["\']?1["\']?/', $envContent)) {
        echo "<div class='success'>✓ System is marked as installed</div>";
    } else {
        echo "<div class='error'>✗ System is NOT marked as installed</div>";
    }
    
    // Check database configuration
    if (preg_match('/DB_DATABASE\s*=\s*["\']?([^"\'\s]+)["\']?/', $envContent, $matches)) {
        $dbName = $matches[1];
        if (!empty($dbName)) {
            echo "<div class='success'>✓ Database name configured: $dbName</div>";
        } else {
            echo "<div class='error'>✗ Database name is empty</div>";
        }
    } else {
        echo "<div class='error'>✗ Database name not found in .env</div>";
    }
} else {
    echo "<div class='error'>✗ .env file not found</div>";
}

// Test 5: Test sitemap generation
echo "<h2>5. Sitemap Generation Test</h2>";

// Capture output from sitemap.xml.php
ob_start();
$_SERVER['HTTP_HOST'] = $_SERVER['HTTP_HOST'] ?? 'localhost';
$_SERVER['REQUEST_URI'] = '/sitemap.xml';
$_SERVER['HTTPS'] = $_SERVER['HTTPS'] ?? 'on';

try {
    include 'sitemap.xml.php';
    $sitemapOutput = ob_get_contents();
    ob_end_clean();
    
    if (!empty($sitemapOutput)) {
        echo "<div class='success'>✓ Sitemap generated successfully</div>";
        echo "<div class='info'>Output length: " . strlen($sitemapOutput) . " bytes</div>";
        
        // Check if it's valid XML
        $dom = new DOMDocument();
        if ($dom->loadXML($sitemapOutput)) {
            echo "<div class='success'>✓ XML is well-formed</div>";
            
            // Count URLs
            $urlCount = substr_count($sitemapOutput, '<url>');
            echo "<div class='info'>Number of URLs: $urlCount</div>";
            
        } else {
            echo "<div class='error'>✗ XML is malformed</div>";
        }
        
        // Show first 500 characters
        echo "<h3>Sitemap Preview:</h3>";
        echo "<pre>" . htmlspecialchars(substr($sitemapOutput, 0, 500)) . "...</pre>";
        
    } else {
        echo "<div class='error'>✗ Sitemap output is empty</div>";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<div class='error'>✗ Error generating sitemap: " . $e->getMessage() . "</div>";
}

// Test 6: Check debug log
echo "<h2>6. Debug Log</h2>";
$logFile = '../storage/logs/sitemap-debug.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    if (!empty($logContent)) {
        echo "<div class='info'>Debug log found:</div>";
        echo "<pre>" . htmlspecialchars(tail($logContent, 20)) . "</pre>";
    } else {
        echo "<div class='info'>Debug log file exists but is empty</div>";
    }
} else {
    echo "<div class='info'>No debug log file found</div>";
}

// Test 7: Direct URL tests
echo "<h2>7. URL Tests</h2>";
$baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$currentDir = dirname($_SERVER['REQUEST_URI']);
if ($currentDir !== '/') {
    $baseUrl .= $currentDir;
}

echo "<div class='info'>Test these URLs:</div>";
echo "<ul>";
echo "<li><a href='{$baseUrl}/sitemap.xml' target='_blank'>{$baseUrl}/sitemap.xml</a> (Raw XML)</li>";
echo "<li><a href='{$baseUrl}/sitemap-viewer.php' target='_blank'>{$baseUrl}/sitemap-viewer.php</a> (HTML View)</li>";
echo "<li><a href='{$baseUrl}/sitemap.xml?styled=1' target='_blank'>{$baseUrl}/sitemap.xml?styled=1</a> (Styled XML)</li>";
echo "</ul>";

// Helper function to get last N lines of a file
function tail($string, $lines = 10) {
    $lines_array = explode("\n", $string);
    return implode("\n", array_slice($lines_array, -$lines));
}

echo "<h2>8. Recommendations</h2>";
echo "<div class='info'>";
echo "<ul>";
echo "<li>If sitemap.xml shows blank, check the debug log above for errors</li>";
echo "<li>Ensure all required PHP extensions are loaded</li>";
echo "<li>Verify file permissions are correct (644 for PHP files)</li>";
echo "<li>Complete Laravel installation if system is not installed</li>";
echo "<li>Check server error logs in DirectAdmin panel</li>";
echo "</ul>";
echo "</div>";

echo "<p><small>Debug completed at " . date('Y-m-d H:i:s') . "</small></p>";
?>
