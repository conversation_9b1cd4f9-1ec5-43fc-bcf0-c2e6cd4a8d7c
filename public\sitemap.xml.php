<?php
/**
 * Bulletproof Sitemap Generator for VPS/DirectAdmin
 * This file generates a sitemap.xml that works reliably on live servers
 * Completely rewritten for maximum compatibility and error handling
 */

// Disable output buffering to prevent blank pages
while (ob_get_level()) {
    ob_end_clean();
}

// Enable error logging but disable display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Create a simple error log function
function logSitemapError($message) {
    $logFile = __DIR__ . '/../storage/logs/sitemap-debug.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        @mkdir($logDir, 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    @file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);

    // Also try to log to PHP error log
    @error_log("Sitemap Error: $message");
}

// Log start of execution
logSitemapError("Sitemap generation started");

// Intelligent detection for styling
function isSearchEngineBot() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    $searchEngineBots = [
        'Googlebot', 'Bingbot', 'Slurp', 'DuckDuckBot', 'Baiduspider', 'YandexBot',
        'facebookexternalhit', 'Twitterbot', 'LinkedInBot', 'WhatsApp', 'Applebot',
        'ia_archiver', 'SeznamBot', 'MJ12bot', 'AhrefsBot', 'SemrushBot', 'crawler',
        'spider', 'bot', 'UptimeRobot', 'StatusCake', 'Pingdom', 'GTmetrix', 'PageSpeed'
    ];

    foreach ($searchEngineBots as $bot) {
        if (stripos($userAgent, $bot) !== false) {
            logSitemapError("Search engine bot detected: $bot");
            return true;
        }
    }

    // Additional bot-like checks
    if (empty($userAgent) ||
        stripos($userAgent, 'curl') !== false ||
        stripos($userAgent, 'wget') !== false ||
        stripos($userAgent, 'python') !== false) {
        logSitemapError("Bot-like user agent detected: $userAgent");
        return true;
    }

    return false;
}

function shouldDisplayStyled() {
    // Always style if explicitly requested
    if (isset($_GET['styled']) && $_GET['styled'] === '1') {
        return true;
    }

    // Don't style if explicitly disabled
    if (isset($_GET['styled']) && $_GET['styled'] === '0') {
        return false;
    }

    // Check Accept header for XML preference
    $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
    if (stripos($acceptHeader, 'application/xml') !== false &&
        stripos($acceptHeader, 'text/html') === false) {
        return false; // Client specifically wants XML
    }

    // Default to styling for browsers
    return true;
}

// Determine styling based on user agent and preferences
$isSearchEngineBot = isSearchEngineBot();
$shouldStyleForHumans = !$isSearchEngineBot && shouldDisplayStyled();

logSitemapError("User agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown'));
logSitemapError("Is search engine bot: " . ($isSearchEngineBot ? 'yes' : 'no'));
logSitemapError("Should style for humans: " . ($shouldStyleForHumans ? 'yes' : 'no'));

// Get the base URL with robust detection
function getBaseUrl() {
    try {
        $protocol = 'http';
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $protocol = 'https';
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $protocol = 'https';
        } elseif (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443) {
            $protocol = 'https';
        }

        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
        $baseUrl = $protocol . '://' . $host;

        // Handle subdirectory installations
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

        // Remove sitemap.xml from the path
        $requestUri = str_replace('/sitemap.xml', '', $requestUri);
        $requestUri = str_replace('/sitemap.xml.php', '', $requestUri);

        // Remove query string
        $requestUri = strtok($requestUri, '?');

        // Clean up the path
        $basePath = rtrim($requestUri, '/');
        if (!empty($basePath)) {
            $baseUrl .= $basePath;
        }

        return $baseUrl;
    } catch (Exception $e) {
        logSitemapError("Error getting base URL: " . $e->getMessage());
        return 'https://localhost';
    }
}

$baseUrl = getBaseUrl();
logSitemapError("Base URL detected: $baseUrl");

// Generate a bulletproof minimal sitemap
function generateMinimalSitemap($baseUrl, $shouldStyleForHumans = false) {
    try {
        logSitemapError("Generating minimal sitemap for: $baseUrl");

        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;

        // Add XSL stylesheet for human browsers (not search engine bots)
        if ($shouldStyleForHumans && file_exists(__DIR__ . '/sitemap.xsl')) {
            $xml .= '<?xml-stylesheet type="text/xsl" href="' . $baseUrl . '/sitemap.xsl"?>' . PHP_EOL;
            logSitemapError("Added XSL stylesheet reference for human browser");
        } else {
            logSitemapError("Serving clean XML for search engine bot or XSL not available");
        }

        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"' . PHP_EOL;
        $xml .= '        xmlns:xhtml="http://www.w3.org/1999/xhtml">' . PHP_EOL;
        $xml .= '  <!-- Generated by 1Sec-mail on ' . date('Y-m-d H:i:s T') . ' -->' . PHP_EOL;

    // Add homepage
    $xml .= '  <url>' . PHP_EOL;
    $xml .= '    <loc>' . htmlspecialchars($baseUrl . '/') . '</loc>' . PHP_EOL;
    $xml .= '    <lastmod>' . date('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
    $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
    $xml .= '    <priority>1.0</priority>' . PHP_EOL;
    $xml .= '  </url>' . PHP_EOL;

    // Add common static pages
    $commonPages = [
        '/about' => ['priority' => '0.8', 'changefreq' => 'monthly'],
        '/contact' => ['priority' => '0.7', 'changefreq' => 'monthly'],
        '/privacy' => ['priority' => '0.6', 'changefreq' => 'yearly'],
        '/terms' => ['priority' => '0.6', 'changefreq' => 'yearly'],
        '/help' => ['priority' => '0.7', 'changefreq' => 'monthly'],
        '/faq' => ['priority' => '0.7', 'changefreq' => 'monthly'],
    ];

    foreach ($commonPages as $page => $config) {
        $xml .= '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($baseUrl . $page) . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . date('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>' . $config['changefreq'] . '</changefreq>' . PHP_EOL;
        $xml .= '    <priority>' . $config['priority'] . '</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;
    }

        $xml .= '</urlset>' . PHP_EOL;

        logSitemapError("Minimal sitemap generated successfully, length: " . strlen($xml));
        return $xml;

    } catch (Exception $e) {
        logSitemapError("Error generating minimal sitemap: " . $e->getMessage());

        // Ultra-minimal fallback
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
        $xml .= '  <url>' . PHP_EOL;
        $xml .= '    <loc>' . htmlspecialchars($baseUrl . '/') . '</loc>' . PHP_EOL;
        $xml .= '    <lastmod>' . date('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
        $xml .= '    <changefreq>daily</changefreq>' . PHP_EOL;
        $xml .= '    <priority>1.0</priority>' . PHP_EOL;
        $xml .= '  </url>' . PHP_EOL;
        $xml .= '</urlset>' . PHP_EOL;
        return $xml;
    }
}

// Main execution logic - simplified and bulletproof
$sitemapContent = '';

// Try Laravel first if system is installed
try {
    logSitemapError("Checking for Laravel installation");

    if (file_exists(__DIR__ . '/../vendor/autoload.php') &&
        file_exists(__DIR__ . '/../bootstrap/app.php')) {

        logSitemapError("Laravel files found, checking installation status");

        // Check if system is installed
        $envFile = __DIR__ . '/../.env';
        $systemInstalled = false;

        if (file_exists($envFile)) {
            $envContent = @file_get_contents($envFile);
            if ($envContent && preg_match('/SYSTEM_INSTALLED\s*=\s*["\']?1["\']?/', $envContent)) {
                $systemInstalled = true;
                logSitemapError("System is installed, attempting Laravel bootstrap");
            }
        }

        if ($systemInstalled) {
            // Try to bootstrap Laravel
            try {
                require_once __DIR__ . '/../vendor/autoload.php';
                $app = require_once __DIR__ . '/../bootstrap/app.php';

                // Create a proper request context with styling parameter
                $params = $shouldStyleForHumans ? ['styled' => '1'] : [];
                $request = \Illuminate\Http\Request::create('/sitemap.xml', 'GET', $params);

                // Set the user agent for proper detection
                $request->headers->set('User-Agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
                $request->headers->set('Accept', $_SERVER['HTTP_ACCEPT'] ?? '');

                $app->instance('request', $request);

                // Initialize the application
                $kernel = $app->make(\Illuminate\Contracts\Http\Kernel::class);

                // Try to get sitemap from Laravel controller
                $sitemapController = new \App\Http\Controllers\SitemapController();
                $response = $sitemapController->index();

                if ($response && $response->getStatusCode() === 200) {
                    $content = $response->getContent();
                    if (!empty($content) && strpos($content, '<urlset') !== false) {
                        $sitemapContent = $content;
                        logSitemapError("Laravel sitemap generated successfully");
                    } else {
                        logSitemapError("Laravel returned empty or invalid content");
                    }
                } else {
                    logSitemapError("Laravel controller returned error status");
                }

            } catch (Exception $laravelException) {
                logSitemapError("Laravel bootstrap failed: " . $laravelException->getMessage());
            }
        } else {
            logSitemapError("System not installed, using fallback");
        }
    } else {
        logSitemapError("Laravel files not found, using fallback");
    }
} catch (Exception $e) {
    logSitemapError("Error in Laravel attempt: " . $e->getMessage());
}

// If Laravel didn't work, use fallback
if (empty($sitemapContent)) {
    logSitemapError("Using minimal sitemap fallback");
    $sitemapContent = generateMinimalSitemap($baseUrl, $shouldStyleForHumans);
}

// Validate XML before output
try {
    $dom = new DOMDocument();
    if (!$dom->loadXML($sitemapContent)) {
        throw new Exception("XML validation failed");
    }
    logSitemapError("XML validation passed");
} catch (Exception $e) {
    logSitemapError("XML validation failed: " . $e->getMessage() . ", using ultra minimal sitemap");

    // Last resort - ultra minimal sitemap
    $sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
    $sitemapContent .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
    $sitemapContent .= '  <url>' . PHP_EOL;
    $sitemapContent .= '    <loc>' . htmlspecialchars($baseUrl . '/') . '</loc>' . PHP_EOL;
    $sitemapContent .= '    <lastmod>' . date('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
    $sitemapContent .= '    <changefreq>daily</changefreq>' . PHP_EOL;
    $sitemapContent .= '    <priority>1.0</priority>' . PHP_EOL;
    $sitemapContent .= '  </url>' . PHP_EOL;
    $sitemapContent .= '</urlset>' . PHP_EOL;
}

// Set headers and output the sitemap
try {
    // Clear any previous output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set proper headers
    header('Content-Type: application/xml; charset=UTF-8');
    header('Cache-Control: public, max-age=3600, must-revalidate');
    header('X-Robots-Tag: noindex, follow');
    header('Content-Length: ' . strlen($sitemapContent));

    // Output the sitemap
    echo $sitemapContent;

    logSitemapError("Sitemap output completed successfully, length: " . strlen($sitemapContent));

} catch (Exception $e) {
    logSitemapError("Error outputting sitemap: " . $e->getMessage());

    // Emergency fallback - just output basic XML
    echo '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . PHP_EOL;
    echo '  <url>' . PHP_EOL;
    echo '    <loc>' . htmlspecialchars($baseUrl . '/') . '</loc>' . PHP_EOL;
    echo '    <lastmod>' . date('Y-m-d\TH:i:s\Z') . '</lastmod>' . PHP_EOL;
    echo '    <changefreq>daily</changefreq>' . PHP_EOL;
    echo '    <priority>1.0</priority>' . PHP_EOL;
    echo '  </url>' . PHP_EOL;
    echo '</urlset>' . PHP_EOL;
}

exit;
?>
